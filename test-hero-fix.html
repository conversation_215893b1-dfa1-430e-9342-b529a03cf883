<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Section Test - FIXED</title>
    <style>
        :root {
            --forest-green: #2f6130;
            --warm-brown: #8b5e3c;
            --soft-beige: #f5f5dc;
            --sky-blue: #7dc8f7;
            --white: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
        }

        /* FIXED Hero Section - Proper Layout */
        .hero-section {
            position: relative;
            width: 100vw;
            height: 80vh;
            min-height: 600px;
            max-height: 800px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--forest-green) 0%, var(--warm-brown) 100%);
            overflow: visible;
            margin: 0;
            padding: 0;
        }

        /* FIXED Container - No conflicts */
        .hero-section .container {
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 100;
        }

        /* FIXED Background Elements */
        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(47, 97, 48, 0.7) 0%, rgba(30, 63, 32, 0.8) 100%);
            z-index: 2;
        }

        /* FIXED Hero Content - No Conflicts */
        .hero-content {
            position: relative;
            z-index: 100;
            color: white;
            text-align: center;
            width: 100%;
        }

        /* FIXED Title and Text - Clear and Visible */
        .hero-section h1 {
            font-size: 3.5rem;
            margin: 0 0 1.5rem 0;
            font-weight: 800;
            color: white !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            line-height: 1.2;
            width: 100%;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .hero-section p {
            font-size: 1.3rem;
            margin: 0 auto 3rem auto;
            max-width: 700px;
            color: white !important;
            opacity: 0.95;
            line-height: 1.5;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        /* FIXED Zoo Finder - Clean White Background */
        .zoo-finder {
            background: white !important;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin: 3rem auto 0 auto;
            max-width: 600px;
            position: relative;
            z-index: 1;
            border: 3px solid #f5f5dc;
        }

        /* FIXED Zoo Finder Elements */
        .zoo-finder h3 {
            color: #2f6130 !important;
            margin-bottom: 1.5rem;
            font-size: 1.4rem;
            font-weight: 700;
            text-align: center;
        }

        .zoo-finder select,
        .zoo-finder input {
            width: 100% !important;
            padding: 16px 20px;
            margin-bottom: 1.5rem;
            border: 2px solid #ddd !important;
            border-radius: 10px;
            font-size: 1.1rem;
            background: #f9f9f9 !important;
            color: #333 !important;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        /* FIXED Focus and Button States */
        .zoo-finder select:focus,
        .zoo-finder input:focus {
            outline: none !important;
            border-color: #7dc8f7 !important;
            box-shadow: 0 0 0 3px rgba(125, 200, 247, 0.2) !important;
            background: white !important;
        }

        .zoo-finder button {
            background: linear-gradient(135deg, #2f6130 0%, #4a8c4a 100%) !important;
            color: white !important;
            padding: 16px 30px;
            border: none !important;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            min-height: 48px;
        }

        .zoo-finder button:hover {
            background: linear-gradient(135deg, #4a8c4a 0%, #2f6130 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(47, 97, 48, 0.3);
        }

        .zoo-finder .btn-secondary {
            background: linear-gradient(135deg, #8b5e3c 0%, #a67c52 100%) !important;
            margin-bottom: 0;
        }

        .zoo-finder .btn-secondary:hover {
            background: linear-gradient(135deg, #a67c52 0%, #8b5e3c 100%) !important;
            box-shadow: 0 4px 15px rgba(139, 94, 60, 0.3);
        }

        /* FIXED MOBILE RESPONSIVE */
        @media (max-width: 768px) {
            .hero-section {
                height: 70vh !important;
                min-height: 500px !important;
                max-height: 600px !important;
            }

            .hero-section .container {
                padding: 0 1rem !important;
            }

            .hero-section h1 {
                font-size: 2.5rem !important;
                margin-bottom: 1rem !important;
            }

            .hero-section p {
                font-size: 1.1rem !important;
                margin-bottom: 2rem !important;
            }

            .zoo-finder {
                padding: 2rem !important;
                margin-top: 2rem !important;
                max-width: 90% !important;
            }
        }
    </style>
</head>
<body>
    <!-- FIXED Hero Section -->
    <section class="hero-section">
        <div class="hero-background">
            <div class="hero-overlay"></div>
        </div>
        <div class="hero-content">
            <div class="container">
                <h1>Discover the Best Petting Zoos Near You</h1>
                <p>Find amazing petting zoos, animal farms, and interactive experiences perfect for families. Create lasting memories with hands-on animal encounters that kids and dads love.</p>

                <!-- FIXED Zoo Finder Tool -->
                <div class="zoo-finder">
                    <h3>🔍 Discover Petting Zoos Near You</h3>
                    <form>
                        <select aria-label="Select your city">
                            <option value="">🏙️ Select Your City</option>
                            <option value="new-york">New York, NY</option>
                            <option value="los-angeles">Los Angeles, CA</option>
                            <option value="chicago">Chicago, IL</option>
                        </select>

                        <select aria-label="Select animal type">
                            <option value="">🐾 Any Animal Type</option>
                            <option value="goats">Goats</option>
                            <option value="sheep">Sheep</option>
                            <option value="rabbits">Rabbits</option>
                        </select>

                        <button type="submit" class="btn">🔍 Find Petting Zoos</button>
                        <button type="button" class="btn btn-secondary">📍 Find Near Me</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <div style="padding: 2rem; text-align: center; background: #f5f5f5;">
        <h2>Test Complete - Hero Section Should Now Display Properly</h2>
        <p>The title should be fully visible, zoo finder should have clean white background, and no overlapping elements.</p>
    </div>
</body>
</html>
