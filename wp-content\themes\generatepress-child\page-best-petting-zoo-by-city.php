<?php
/**
 * Page Template for Best Petting Zoo by City
 * This template automatically applies to the page with slug "best-petting-zoo-by-city"
 */

get_header(); ?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('best-petting-zoo-by-city-page'); ?>>
                <div class="container">
                    
                    <!-- Page Header -->
                    <header class="entry-header">
                        <h1 class="entry-title"><?php the_title(); ?></h1>
                    </header>

                    <!-- Page Content -->
                    <div class="entry-content">
                        <?php the_content(); ?>
                    </div>

                    <!-- States and Cities Grid Section -->
                    <section class="section popular-cities-section">
                        <div class="container">
                            <h2 class="section-title">Browse Petting Zoos by State and City</h2>
                            <p class="section-subtitle">Explore our comprehensive directory of petting zoos organized by location. Click on any city to discover the best family-friendly animal experiences in that area.</p>

                            <div class="state-cards-grid">
                                <?php
                                // Read and parse the CSV file - Same as homepage
                                $upload_dir = wp_upload_dir();
                                $csv_file = $upload_dir['basedir'] . '/2025/06/city-state.csv';
                                $states_data = array();

                                if (file_exists($csv_file)) {
                                    $handle = fopen($csv_file, 'r');
                                    if ($handle !== FALSE) {
                                        $header = fgetcsv($handle); // Skip header row

                                        while (($data = fgetcsv($handle)) !== FALSE) {
                                            if (!empty($data[0]) && !empty($data[2])) {
                                                $city = trim($data[0]);
                                                $state = trim($data[2]);

                                                if (!isset($states_data[$state])) {
                                                    $states_data[$state] = array();
                                                }
                                                $states_data[$state][] = $city;
                                            }
                                        }
                                        fclose($handle);
                                    }
                                } else {
                                    // Debug: Show file path if CSV not found
                                    echo '<!-- CSV file not found at: ' . esc_html($csv_file) . ' -->';
                                    echo '<!-- Upload dir: ' . esc_html($upload_dir['basedir']) . ' -->';
                                    echo '<!-- File exists check: ' . (file_exists($csv_file) ? 'true' : 'false') . ' -->';
                                }

                                // Sort states alphabetically and limit cities per state
                                ksort($states_data);
                                $displayed_states = 0;
                                $max_states = 60; // Display states in 3 columns

                                if (!empty($states_data)) {
                                    foreach ($states_data as $state => $cities) {
                                        if ($displayed_states >= $max_states) break;

                                        // Limit to 12 cities per state for better display
                                        $cities = array_slice($cities, 0, 12);
                                        ?>
                                        <div class="state-card">
                                            <h3 class="state-title">Petting Zoos in <?php echo esc_html($state); ?></h3>
                                            <div class="cities-list">
                                                <?php foreach ($cities as $city) : ?>
                                                    <a href="/city/<?php echo sanitize_title($city); ?>-<?php echo sanitize_title($state); ?>/" class="city-link">
                                                        <?php echo esc_html($city); ?>
                                                    </a>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                        <?php
                                        $displayed_states++;
                                    }
                                } else {
                                    echo '<div class="no-data-message">';
                                    echo '<p>🏙️ We\'re currently building our city directory. Check back soon for more locations!</p>';
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </section>

                    <!-- FAQ Section -->
                    <section class="section faq-section">
                        <div class="container">
                            <h2 class="section-title">Frequently Asked Questions</h2>
                            <p class="section-subtitle">Get answers to common questions about visiting petting zoos with your family.</p>

                            <div class="faq-accordion">
                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">What age groups are appropriate for petting zoo visits?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Most petting zoos welcome visitors of all ages, though children under 3 should be closely supervised during animal interactions. Many facilities offer age-appropriate activities designed specifically for toddlers, preschoolers, and school-age children.</p>
                                    </div>
                                </div>

                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">Are petting zoos safe for children with allergies?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Contact the facility directly to discuss specific allergy concerns. Most petting zoos can provide information about animals present and recommend precautions for visitors with known allergies to animal dander or specific species.</p>
                                    </div>
                                </div>

                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">Can I bring my own food to a petting zoo?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Food policies vary by facility. Some petting zoos allow outside food in designated picnic areas, while others restrict outside food to maintain animal health and safety. Check specific facility policies before your visit.</p>
                                    </div>
                                </div>

                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">What should I wear to a petting zoo?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Wear comfortable, closed-toe shoes and clothing that you don't mind getting dirty. Avoid loose jewelry or accessories that might pose safety risks during animal interactions.</p>
                                    </div>
                                </div>

                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">Do petting zoos offer educational programs?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Many petting zoos provide educational programming including guided tours, animal care demonstrations, and curriculum-aligned activities for school groups. Contact facilities directly to learn about available educational opportunities.</p>
                                    </div>
                                </div>

                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">How much do petting zoo visits typically cost?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Admission prices vary widely depending on location, size, and amenities offered. Most petting zoos charge between $5-15 per person, with many offering family packages and group discounts. Some facilities include animal feed in admission, while others charge separately.</p>
                                    </div>
                                </div>

                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">What's the best time to visit a petting zoo?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Early morning or late afternoon visits often provide the best experience, as animals are typically more active during cooler parts of the day. Weekdays tend to be less crowded than weekends, offering more personalized interactions with animals and staff.</p>
                                    </div>
                                </div>

                                <div class="faq-item">
                                    <h3 class="faq-question" aria-expanded="false" role="button" tabindex="0">Are petting zoos suitable for birthday parties?</h3>
                                    <div class="faq-answer" aria-hidden="true">
                                        <p>Many petting zoos offer birthday party packages that include private areas, animal interactions, and sometimes additional activities like pony rides or educational presentations. Contact facilities directly to inquire about party options and pricing.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                </div>
            </article>

        <?php endwhile; ?>

    </main>
</div>

<?php get_footer(); ?>
